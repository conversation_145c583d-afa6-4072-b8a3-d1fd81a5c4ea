# openapi-client

API for Continue IDE to fetch assistants and other related information.
These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0
- Package version: 1.0.0
- Generator version: 7.12.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen
  For more information, please visit [https://continue.dev](https://continue.dev)

## Requirements.

Python 3.8+

## Installation & Usage

### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```

(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:

```python
import openapi_client
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```

(or `sudo python setup.py install` to install the package for all users)

Then import the package:

```python
import openapi_client
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import openapi_client
from openapi_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.continue.dev
# See configuration.py for a list of all supported configuration parameters.
configuration = openapi_client.Configuration(
    host = "https://api.continue.dev"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization: apiKeyAuth
configuration = openapi_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)


# Enter a context with an instance of the API client
with openapi_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = openapi_client.DefaultApi(api_client)
    always_use_proxy = 'always_use_proxy_example' # str | Whether to always use the Continue-managed proxy for model requests (optional)
    organization_id = 'organization_id_example' # str | ID of the organization to scope assistants to. If not provided, personal assistants are returned. (optional)

    try:
        # List assistants for IDE
        api_response = api_instance.list_assistants(always_use_proxy=always_use_proxy, organization_id=organization_id)
        print("The response of DefaultApi->list_assistants:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling DefaultApi->list_assistants: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://api.continue.dev*

| Class        | Method                                                    | HTTP request                 | Description             |
| ------------ | --------------------------------------------------------- | ---------------------------- | ----------------------- |
| _DefaultApi_ | [**list_assistants**](docs/DefaultApi.md#list_assistants) | **GET** /ide/list-assistants | List assistants for IDE |

## Documentation For Models

- [ListAssistants200ResponseInner](docs/ListAssistants200ResponseInner.md)
- [ListAssistants200ResponseInnerConfigResult](docs/ListAssistants200ResponseInnerConfigResult.md)
- [ListAssistants401Response](docs/ListAssistants401Response.md)
- [ListAssistants404Response](docs/ListAssistants404Response.md)

<a id="documentation-for-authorization"></a>

## Documentation For Authorization

Authentication schemes defined for the API:
<a id="apiKeyAuth"></a>

### apiKeyAuth

- **Type**: Bearer authentication

## Author
