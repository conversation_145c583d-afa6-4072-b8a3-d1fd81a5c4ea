import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { inferResolvedUriFromRelativePath } from "core/util/ideUtils";
import { useContext, useEffect, useMemo, useState } from "react";
import styled from "styled-components";
import {
  defaultBorderRadius,
  vscCommandCenterInactiveBorder,
  vscEditorBackground,
} from "../..";
import { IdeMessengerContext } from "../../../context/IdeMessenger";
import { useIdeMessengerRequest } from "../../../hooks";
import { useWebviewListener } from "../../../hooks/useWebviewListener";
import { useAppSelector } from "../../../redux/hooks";
import { selectCurrentToolCallApplyState } from "../../../redux/selectors/selectCurrentToolCall";
import { selectApplyStateByStreamId } from "../../../redux/slices/sessionSlice";
import { getFontSize } from "../../../util";
import Spinner from "../../gui/Spinner";
import { isTerminalCodeBlock } from "../utils";
import { ApplyActions } from "./ApplyActions";
import { CopyButton } from "./CopyButton";
import { CreateFileButton } from "./CreateFileButton";
import { FileInfo } from "./FileInfo";
import { InsertButton } from "./InsertButton";
import { RunInTerminalButton } from "./RunInTerminalButton";

/**
 * 使用启发式算法推理代码块应该应用到文件的哪些行
 */
async function inferCodeBlockPosition(
  fileContent: string,
  codeBlockContent: string,
  language: string | null
): Promise<{ inferredStartLine: number; inferredEndLine: number }> {
  const fileLines = fileContent.split('\n');
  const codeLines = codeBlockContent.split('\n').filter(line => line.trim());

  if (codeLines.length === 0) {
    return { inferredStartLine: 0, inferredEndLine: 0 };
  }

  // 首先尝试精确匹配
  const exactMatch = findExactMatch(fileLines, codeLines);
  if (exactMatch) {
    return exactMatch;
  }

  // 然后尝试部分匹配
  const partialMatch = findBestPartialMatch(fileLines, codeLines);
  if (partialMatch && partialMatch.score > 0.3) {
    return {
      inferredStartLine: partialMatch.startLine,
      inferredEndLine: partialMatch.endLine
    };
  }

  // 如果没有找到好的匹配，使用智能默认策略
  return getDefaultPosition(fileLines, codeLines, language);
}

/**
 * 寻找精确匹配的代码段
 */
function findExactMatch(fileLines: string[], codeLines: string[]): { inferredStartLine: number; inferredEndLine: number } | null {
  for (let i = 0; i <= fileLines.length - codeLines.length; i++) {
    let isMatch = true;
    for (let j = 0; j < codeLines.length; j++) {
      if (fileLines[i + j]?.trim() !== codeLines[j].trim()) {
        isMatch = false;
        break;
      }
    }
    if (isMatch) {
      return {
        inferredStartLine: i,
        inferredEndLine: i + codeLines.length - 1
      };
    }
  }
  return null;
}

/**
 * 寻找最佳部分匹配
 */
function findBestPartialMatch(fileLines: string[], codeLines: string[]): { startLine: number; endLine: number; score: number } | null {
  let bestMatch = { startLine: 0, endLine: 0, score: 0 };

  // 动态调整搜索窗口大小
  const minWindowSize = Math.max(codeLines.length, 5);
  const maxWindowSize = Math.min(codeLines.length * 2, 50);

  for (let windowSize = minWindowSize; windowSize <= maxWindowSize; windowSize += 5) {
    for (let i = 0; i <= Math.max(0, fileLines.length - windowSize); i++) {
      const windowEnd = Math.min(i + windowSize - 1, fileLines.length - 1);
      const windowLines = fileLines.slice(i, windowEnd + 1);

      const score = calculateSimilarityScore(windowLines, codeLines);

      if (score > bestMatch.score) {
        bestMatch = {
          startLine: i,
          endLine: windowEnd,
          score
        };
      }
    }
  }

  return bestMatch.score > 0 ? bestMatch : null;
}

/**
 * 获取默认位置策略
 */
function getDefaultPosition(fileLines: string[], codeLines: string[], language: string | null): { inferredStartLine: number; inferredEndLine: number } {
  // 检查是否是函数定义
  const firstCodeLine = codeLines[0]?.trim();
  if (firstCodeLine && isLikelyFunctionDefinition(firstCodeLine, language)) {
    // 函数定义通常放在文件末尾
    const insertLine = Math.max(0, fileLines.length - 1);
    return {
      inferredStartLine: insertLine,
      inferredEndLine: insertLine
    };
  }

  // 检查是否是导入语句
  if (firstCodeLine && isLikelyImportStatement(firstCodeLine, language)) {
    // 导入语句通常放在文件开头
    return {
      inferredStartLine: 0,
      inferredEndLine: Math.min(5, fileLines.length - 1)
    };
  }

  // 默认情况：放在文件中间偏后的位置
  const middleLine = Math.floor(fileLines.length * 0.7);
  const endLine = Math.min(middleLine + 10, fileLines.length - 1);
  return {
    inferredStartLine: middleLine,
    inferredEndLine: endLine
  };
}

/**
 * 判断是否可能是函数定义
 */
function isLikelyFunctionDefinition(line: string, language: string | null): boolean {
  const functionKeywords = ['function', 'def', 'func', 'fn', 'method', 'class', 'interface'];
  const lowerLine = line.toLowerCase();
  return functionKeywords.some(keyword => lowerLine.includes(keyword));
}

/**
 * 判断是否可能是导入语句
 */
function isLikelyImportStatement(line: string, language: string | null): boolean {
  const importKeywords = ['import', 'from', 'require', 'include', '#include', 'using'];
  const lowerLine = line.toLowerCase();
  return importKeywords.some(keyword => lowerLine.startsWith(keyword));
}

/**
 * 计算两个代码片段的相似度分数（优化版本）
 */
function calculateSimilarityScore(windowLines: string[], codeLines: string[]): number {
  if (windowLines.length === 0 || codeLines.length === 0) {
    return 0;
  }

  let totalScore = 0;
  let maxPossibleScore = 0;

  // 为每个代码行找到最佳匹配
  for (const codeLine of codeLines) {
    const trimmedCodeLine = codeLine.trim();
    if (!trimmedCodeLine) continue;

    let bestLineScore = 0;
    maxPossibleScore += 1;

    for (const windowLine of windowLines) {
      const trimmedWindowLine = windowLine.trim();
      if (!trimmedWindowLine) continue;

      const lineScore = calculateLineScore(trimmedCodeLine, trimmedWindowLine);
      bestLineScore = Math.max(bestLineScore, lineScore);
    }

    totalScore += bestLineScore;
  }

  return maxPossibleScore > 0 ? totalScore / maxPossibleScore : 0;
}

/**
 * 计算两行代码的相似度分数
 */
function calculateLineScore(codeLine: string, windowLine: string): number {
  // 精确匹配
  if (codeLine === windowLine) {
    return 1.0;
  }

  // 去除空格后匹配
  if (codeLine.replace(/\s+/g, '') === windowLine.replace(/\s+/g, '')) {
    return 0.9;
  }

  // 包含关系匹配
  if (codeLine.includes(windowLine) || windowLine.includes(codeLine)) {
    const longer = codeLine.length > windowLine.length ? codeLine : windowLine;
    const shorter = codeLine.length <= windowLine.length ? codeLine : windowLine;
    return (shorter.length / longer.length) * 0.7;
  }

  // 关键词匹配
  const codeWords = codeLine.split(/\W+/).filter(w => w.length > 2);
  const windowWords = windowLine.split(/\W+/).filter(w => w.length > 2);

  if (codeWords.length === 0 || windowWords.length === 0) {
    return 0;
  }

  const commonWords = codeWords.filter(word => windowWords.includes(word));
  const keywordScore = commonWords.length / Math.max(codeWords.length, windowWords.length);

  // 结构相似性（括号、分号等）
  const structureScore = calculateStructureScore(codeLine, windowLine);

  return Math.max(keywordScore * 0.6, structureScore * 0.4);
}

/**
 * 计算结构相似性分数
 */
function calculateStructureScore(line1: string, line2: string): number {
  const getStructurePattern = (line: string) => {
    return line.replace(/[a-zA-Z0-9_]/g, 'X').replace(/\s+/g, '');
  };

  const pattern1 = getStructurePattern(line1);
  const pattern2 = getStructurePattern(line2);

  if (pattern1 === pattern2) {
    return 1.0;
  }

  // 计算编辑距离
  const editDistance = calculateEditDistance(pattern1, pattern2);
  const maxLength = Math.max(pattern1.length, pattern2.length);

  return maxLength > 0 ? 1 - (editDistance / maxLength) : 0;
}

/**
 * 计算编辑距离（简化版）
 */
function calculateEditDistance(str1: string, str2: string): number {
  const matrix = Array(str1.length + 1).fill(null).map(() => Array(str2.length + 1).fill(0));

  for (let i = 0; i <= str1.length; i++) matrix[i][0] = i;
  for (let j = 0; j <= str2.length; j++) matrix[0][j] = j;

  for (let i = 1; i <= str1.length; i++) {
    for (let j = 1; j <= str2.length; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + 1
        );
      }
    }
  }

  return matrix[str1.length][str2.length];
}

const TopDiv = styled.div`
  display: flex;
  flex-direction: column;
  outline: 1px solid ${vscCommandCenterInactiveBorder};
  outline-offset: -0.5px;
  border-radius: ${defaultBorderRadius};
  margin-bottom: 8px !important;
  margin-top: 8px !important;
  background-color: ${vscEditorBackground};
  min-width: 0;
`;

const ToolbarDiv = styled.div<{ isExpanded: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${getFontSize() - 2}px;
  padding: 4px 6px;
  margin: 0;
  border-bottom: ${({ isExpanded }) =>
    isExpanded ? `1px solid ${vscCommandCenterInactiveBorder}` : "inherit"};
`;

export interface StepContainerPreToolbarProps {
  codeBlockContent: string;
  language: string | null;
  relativeFilepath?: string;
  itemIndex?: number;
  codeBlockIndex: number; // To track which codeblock we are applying
  isLastCodeblock: boolean;
  codeBlockStreamId: string;
  range?: string;
  children: any;
  expanded?: boolean;
  disableManualApply?: boolean;
}

export function StepContainerPreToolbar({
  codeBlockContent,
  language,
  relativeFilepath,
  itemIndex,
  codeBlockIndex,
  isLastCodeblock,
  codeBlockStreamId,
  range,
  children,
  expanded,
  disableManualApply,
}: StepContainerPreToolbarProps) {
  const ideMessenger = useContext(IdeMessengerContext);
  const history = useAppSelector((state) => state.session.history);
  const [isExpanded, setIsExpanded] = useState(expanded ?? true);

  const [relativeFilepathUri, setRelativeFilepathUri] = useState<string | null>(
    null,
  );

  const fileExistsInput = useMemo(
    () => (relativeFilepathUri ? { filepath: relativeFilepathUri } : null),
    [relativeFilepathUri],
  );

  const {
    result: fileExists,
    refresh: refreshFileExists,
    isLoading: isLoadingFileExists,
  } = useIdeMessengerRequest("fileExists", fileExistsInput);

  const nextCodeBlockIndex = useAppSelector(
    (state) => state.session.codeBlockApplyStates.curIndex,
  );

  const applyState = useAppSelector((state) =>
    selectApplyStateByStreamId(state, codeBlockStreamId),
  );
  const currentToolCallApplyState = useAppSelector(
    selectCurrentToolCallApplyState,
  );

  /**
   * In the case where `relativeFilepath` is defined, this will just be `relativeFilepathUri`.
   * However, if no `relativeFilepath` is defined, then this will
   * be the URI of the currently open file at the time the user clicks "Apply".
   */
  const [appliedFileUri, setAppliedFileUri] = useState<string | undefined>(
    undefined,
  );

  const isNextCodeBlock = nextCodeBlockIndex === codeBlockIndex;
  const hasFileExtension =
    relativeFilepath && /\.[0-9a-z]+$/i.test(relativeFilepath);

  const isStreaming = useAppSelector((store) => store.session.isStreaming);

  const isLastItem = useMemo(() => {
    return itemIndex === history.length - 1;
  }, [history.length, itemIndex]);

  const isGeneratingCodeBlock = isLastItem && isLastCodeblock && isStreaming;

  // If we are creating a file, we already render that in the button
  // so we don't want to dispaly it twice here
  const displayFilepath = relativeFilepath ?? appliedFileUri;

  // TODO: This logic should be moved to a thunk
  // Handle apply keyboard shortcut
  useWebviewListener(
    "applyCodeFromChat",
    async () => onClickApply(),
    [isNextCodeBlock, codeBlockContent],
    !isNextCodeBlock,
  );

  useEffect(() => {
    const getRelativeFilepathUri = async () => {
      if (relativeFilepath) {
        const resolvedUri = await inferResolvedUriFromRelativePath(
          relativeFilepath,
          ideMessenger.ide,
        );
        setRelativeFilepathUri(resolvedUri);
      }
    };
    getRelativeFilepathUri();
  }, [relativeFilepath, ideMessenger.ide]);

  async function getFileUriToApplyTo() {
    // If we've already resolved a file URI (from clicking apply), use that
    if (appliedFileUri) {
      return appliedFileUri;
    }

    // If we have the `relativeFilepathUri`, use that
    if (relativeFilepathUri) {
      return relativeFilepathUri;
    }

    // If no filepath was provided, get the current file
    const currentFile = await ideMessenger.ide.getCurrentFile();
    if (currentFile) {
      return currentFile.path;
    }

    return undefined;
  }

  async function onClickApply() {
    const fileUri = await getFileUriToApplyTo();
    if (!fileUri) {
      ideMessenger.ide.showToast(
        "error",
        "Could not resolve filepath to apply changes",
      );
      return;
    }

    // Get current file content to analyze where to apply changes
    let startLine = 0;
    let endLine = 0;

    try {
      const fileContent = await ideMessenger.ide.readFile(fileUri);
      if (fileContent && fileContent.trim()) {
        const { inferredStartLine, inferredEndLine } = await inferCodeBlockPosition(
          fileContent,
          codeBlockContent,
          language
        );
        startLine = inferredStartLine;
        endLine = inferredEndLine;
      }
    } catch (error) {
      console.warn("Failed to read file content for position inference:", error);
      // Continue with default values (0, 0) if file reading fails
    }

    // applyToFile will create the file if it doesn't exist
    ideMessenger.post("applyToFile", {
      streamId: codeBlockStreamId,
      filepath: fileUri,
      text: codeBlockContent,
      startLine,
      endLine,
    });

    setAppliedFileUri(fileUri);
    refreshFileExists();
  }

  function onClickInsertAtCursor() {
    ideMessenger.post("insertAtCursor", { text: codeBlockContent });
  }

  async function handleDiffAction(action: "accept" | "reject") {
    const filepath = await getFileUriToApplyTo();
    if (!filepath) {
      ideMessenger.ide.showToast(
        "error",
        `Could not resolve filepath to ${action} changes`,
      );
      return;
    }

    ideMessenger.post(`${action}Diff`, {
      filepath,
      streamId: codeBlockStreamId,
    });

    setAppliedFileUri(undefined);
  }

  async function onClickFilename() {
    if (appliedFileUri) {
      ideMessenger.post("showFile", {
        filepath: appliedFileUri,
      });
    }

    if (relativeFilepath) {
      const filepath = await inferResolvedUriFromRelativePath(
        relativeFilepath,
        ideMessenger.ide,
      );

      ideMessenger.post("showFile", {
        filepath,
      });
    }
  }

  const renderActionButtons = () => {
    const isPendingToolCall =
      currentToolCallApplyState &&
      currentToolCallApplyState.streamId === applyState?.streamId &&
      currentToolCallApplyState.status === "not-started";

    if (isGeneratingCodeBlock || isPendingToolCall) {
      const numLines = codeBlockContent.split("\n").length;
      const plural = numLines === 1 ? "" : "s";
      if (isGeneratingCodeBlock) {
        return (
          <span className="text-lightgray inline-flex items-center gap-2 text-right">
            {!isExpanded ? `${numLines} line${plural}` : "Generating"}{" "}
            <div>
              <Spinner />
            </div>
          </span>
        );
      } else {
        return (
          <span className="text-lightgray inline-flex items-center gap-2 text-right">
            {`${numLines} line${plural} pending`}
          </span>
        );
      }
    }

    if (isTerminalCodeBlock(language, codeBlockContent)) {
      return <RunInTerminalButton command={codeBlockContent} />;
    }

    if (isLoadingFileExists) {
      return null;
    }

    if (fileExists || !relativeFilepath) {
      return (
        <ApplyActions
          disableManualApply={disableManualApply}
          applyState={applyState}
          onClickApply={onClickApply}
          onClickAccept={() => handleDiffAction("accept")}
          onClickReject={() => handleDiffAction("reject")}
        />
      );
    }

    return <CreateFileButton onClick={onClickApply} />;
  };

  // We wait until there is an extension in the filepath to avoid rendering
  // an incomplete filepath
  if (relativeFilepath && !hasFileExtension) {
    return children;
  }

  return (
    <TopDiv>
      <ToolbarDiv isExpanded={isExpanded} className="find-widget-skip gap-3">
        <div className="flex max-w-72 flex-row items-center">
          <ChevronDownIcon
            onClick={() => setIsExpanded(!isExpanded)}
            className={`text-lightgray h-3.5 w-3.5 flex-shrink-0 cursor-pointer hover:brightness-125 ${
              isExpanded ? "rotate-0" : "-rotate-90"
            }`}
          />
          {displayFilepath ? (
            <FileInfo
              filepath={displayFilepath}
              range={range}
              onClick={fileExists ? onClickFilename : undefined}
            />
          ) : (
            <span className="text-lightgray ml-2 select-none capitalize">
              {language}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2.5">
          {!isGeneratingCodeBlock && (
            <div className="xs:flex hidden items-center gap-2.5">
              <InsertButton onInsert={onClickInsertAtCursor} />
              <CopyButton text={codeBlockContent} />
            </div>
          )}

          {renderActionButtons()}
        </div>
      </ToolbarDiv>

      {isExpanded && (
        <div className="overflow-hidden overflow-y-auto">{children}</div>
      )}
    </TopDiv>
  );
}
