import {
  ArrowUpOnSquareIcon,
  Bars3BottomLeftIcon,
  BookOpenIcon,
  BugAntIcon,
  CircleStackIcon,
  CodeBracketIcon,
  CommandLineIcon,
  CpuChipIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  FolderIcon,
  FolderOpenIcon,
  GlobeAltIcon,
  MagnifyingGlassIcon,
  PaperClipIcon,
  PlusIcon,
  SparklesIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { DiscordIcon } from "../svg/DiscordIcon";
import { GithubIcon } from "../svg/GithubIcon";
import { GitlabIcon } from "../svg/GitlabIcon";
import { GoogleIcon } from "../svg/GoogleIcon";

export const NAMED_ICONS: { [key: string]: any } = {
  file: FolderIcon,
  code: CodeBracketIcon,
  terminal: CommandLineIcon,
  diff: PlusIcon,
  search: MagnifyingGlassIcon,
  url: GlobeAltIcon,
  open: FolderOpenIcon,
  codebase: SparklesIcon,
  problems: ExclamationTriangleIcon,
  folder: FolderIcon,
  docs: BookOpenIcon,
  web: GlobeAltIcon,
  clipboard: PaperClipIcon,
  database: CircleStackIcon,
  postgres: CircleStackIcon,
  debugger: BugAntIcon,
  os: CpuChipIcon,
  tree: Bars3BottomLeftIcon,
  "prompt-files": DocumentTextIcon,
  "repo-map": FolderIcon,
  "/clear": TrashIcon,
  "/share": ArrowUpOnSquareIcon,
  "/cmd": CommandLineIcon,
  issue: GithubIcon,
  discord: DiscordIcon,
  google: GoogleIcon,
  "gitlab-mr": GitlabIcon,
  http: GlobeAltIcon,
  trash: TrashIcon,
};
