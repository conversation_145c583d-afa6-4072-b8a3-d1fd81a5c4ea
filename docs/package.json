{"name": "continue-docs", "version": "0.0.0", "author": "Continue Dev, Inc", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "nodemon --watch sidebars.js --watch docusaurus.config.js --exec \"docusaurus start\"", "build": "docusaurus build", "build:netlify": "docusaurus build --out-dir build/docs", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "^3.7.0", "@docusaurus/faster": "^3.7.0", "@docusaurus/plugin-client-redirects": "^3.7.0", "@docusaurus/preset-classic": "^3.7.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.1.0", "prism-react-renderer": "^2.3.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.7.0", "nodemon": "^3.1.4"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engine-strict": true, "engines": {"node": ">=20.19.0"}}