Package Path: com.yinhai.tools.basic.date

public static Date localDateTimeToDate(LocalDateTime localDateTime);

public static Date localDateTimeToDate(LocalDateTime localDateTime, String timeZone);

public static Date localDateToDate(LocalDate localDate);

public static Date localTimeToDate(LocalTime localTime);

public static LocalTime dateToLocalTime(Date date);

public static LocalDateTime dateToLocalDateTime(Date date);

public static Timestamp localDateToTimestamp(LocalDate localDate);

public static Timestamp localTimeToTimestamp(LocalTime localTime);

public static LocalTime timestampToLocalTime(Timestamp timestamp);

public static LocalDateTime timestampToLocalDateTime(Timestamp timestamp);

public static java.sql.Date localDateToSqlDate(LocalDate localDate);

public static LocalDate sqlDateToLocalDate(java.sql.Date date);

public static java.sql.Time localDateTimeToSqlTime(LocalDateTime localDateTime);

public static java.sql.Time localTimeToSqlTime(LocalTime localTime);

public static LocalTime sqlTimeToLocalTime(java.sql.Time time);

public static java.sql.Time dateToSqlTime(Date date);

public static Timestamp dateToTimestamp(Date date);

public static java.sql.Date dateToSqlDate(Date date);

public static String dateToString(Date date, String pattern);

public static String dateToString(LocalDate date, String pattern);

public static String dateToString(java.sql.Date date, String pattern);

public static String dateToString(LocalDateTime date, String pattern);

public static String dateToString(Timestamp timestamp, String pattern);

public static String dateToString(java.sql.Time time, String pattern);

public static LocalDateTime stringToLocalDateTime(String dateStr, TimeFormat timeFormat);

public static LocalDateTime stringToLocalDateTime(String dateStr);

public static LocalDate stringToLocalDate(String dateStr);

public static LocalTime stringToLocalTime(String dateStr);

public static java.util.Date stringToDate(String dateStr);

public static java.sql.Date stringToSqlDate(String dateStr);

public static java.sql.Timestamp stringToTimestamp(String dateStr);

public static java.sql.Time stringToSqlTime(String dateStr);

public static Timestamp stringToTimestamp(String dateStr, String pattern);

public static long toTimestamp(Date date);

public static TimeFormat getCustomPattern(String pattern);

public String getPattern();

