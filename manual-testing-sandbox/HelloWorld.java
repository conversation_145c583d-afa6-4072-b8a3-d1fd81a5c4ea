import java.net.InetAddress;
import java.net.UnknownHostException;

public class HelloWorld {

    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }

    public static String getCurrentTime(String time) {
        if (time.equals("")) {
            return "No time provided";
        }

        String currentTime = String.valueOf(System.currentTimeMillis());

        System.out.println("Current time: " + currentTime);

        for (int i = 0; i < 5; i++) {
            System.out.println("Hello, World!");
        }

        return currentTime;
    }

    public static int add(int a, int b) {
        return a + b;
    }

    public static int subtract(int a, int b) {
        return a - b;
    }

    public static int multiply(int a, int b) {
        return a * b;
    }

    public static int divide(int a, int b) {
        if (b == 0) {
            throw new IllegalArgumentException("Divider cannot be zero");
        }
        return a / b;
    }

    public static String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return null;
        }
    }
}
